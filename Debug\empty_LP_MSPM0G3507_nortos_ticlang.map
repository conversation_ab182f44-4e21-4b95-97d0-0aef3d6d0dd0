******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 09:48:07 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 0000212d


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00002ea0  00005160  R  X
  SRAM                  20200000   00004000  000003e8  00003c18  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00002ea0   00002ea0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002630   00002630    r-x .text
  000026f0    000026f0    00000778   00000778    r-- .rodata
  00002e68    00002e68    00000038   00000038    r-- .cinit
20200000    20200000    000001eb   00000000    rw-
  20200000    20200000    000001d5   00000000    rw- .bss
  202001d8    202001d8    00000013   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002630     
                  000000c0    00000288     tft180.o (.text.tft180_init)
                  00000348    0000020c     encoder.o (.text.encoder_exti_callback)
                  00000554    00000204     openmv.o (.text.openmv_display_data)
                  00000758    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000944    000001bc     tft180.o (.text.func_float_to_str)
                  00000b00    00000130     tft180.o (.text.tft180_show_char_color)
                  00000c30    0000012c     openmv.o (.text.UART3_IRQHandler)
                  00000d5c    0000010c     tft180.o (.text.tft180_show_num_color)
                  00000e68    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  00000f6c    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00001054    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  0000112c    000000d4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001200    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_6_init)
                  0000128c    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_7_init)
                  00001318    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  000013a4    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001428    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000014a4    00000078     openmv.o (.text.openmv_init)
                  0000151c    00000078     tft180.o (.text.tft180_clear_color)
                  00001594    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001608    00000074     delay.o (.text.delay_us)
                  0000167c    0000006c     tft180.o (.text.tft180_set_region)
                  000016e8    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001750    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000017b8    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  0000181a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000181c    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  0000187e    00000062     tft180.o (.text.tft180_show_string_color)
                  000018e0    0000005c     empty.o (.text.main)
                  0000193c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00001992    00000002     empty.o (.text.timerA_callback)
                  00001994    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  000019e8    00000050     openmv.o (.text.openmv_is_data_valid)
                  00001a38    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00001a84    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001acc    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00001b14    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_3_init)
                  00001b5c    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00001ba0    00000044     tft180.o (.text.tft180_write_16bit_data)
                  00001be4    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_IMU660RB_init)
                  00001c24    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TFT_SPI_init)
                  00001c64    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_8_init)
                  00001ca4    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00001ce4    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00001d20    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_12_init)
                  00001d5c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00001d98    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00001dd4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00001e10    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00001e4a    00000002     empty.o (.text.timerB_callback)
                  00001e4c    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00001e86    00000002     --HOLE-- [fill = 0]
                  00001e88    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00001ec0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00001ef4    00000034     openmv.o (.text.openmv_analysis)
                  00001f28    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00001f58    00000030     tft180.o (.text.tft180_write_index)
                  00001f88    0000002c     openmv.o (.text.__NVIC_ClearPendingIRQ)
                  00001fb4    0000002c     timer.o (.text.__NVIC_ClearPendingIRQ)
                  00001fe0    0000002c     openmv.o (.text.__NVIC_EnableIRQ)
                  0000200c    0000002c     timer.o (.text.__NVIC_EnableIRQ)
                  00002038    0000002c     tft180.o (.text.tft180_write_8bit_data)
                  00002064    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000208c    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  000020b4    00000028     timer.o (.text.TIMG8_IRQHandler)
                  000020dc    00000028     debug.o (.text.UART0_IRQHandler)
                  00002104    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  0000212c    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002154    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  00002178    00000022     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000219a    00000002     --HOLE-- [fill = 0]
                  0000219c    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000021bc    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000021da    00000002     --HOLE-- [fill = 0]
                  000021dc    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  000021f8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00002214    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00002230    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  0000224c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00002268    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002284    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  000022a0    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000022bc    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000022d8    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000022f4    0000001c     timer.o (.text.TIMG12_IRQHandler)
                  00002310    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002328    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002340    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00002358    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002370    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002388    00000018     tft180.o (.text.DL_GPIO_setPins)
                  000023a0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000023b8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000023d0    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  000023e8    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  00002400    00000018     tft180.o (.text.DL_SPI_isBusy)
                  00002418    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  00002430    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002448    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002460    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002478    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002490    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000024a8    00000018     openmv.o (.text.DL_UART_isRXFIFOEmpty)
                  000024c0    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000024d8    00000016     encoder.o (.text.DL_GPIO_readPins)
                  000024ee    00000016     tft180.o (.text.DL_SPI_transmitData8)
                  00002504    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  0000251a    00000016     delay.o (.text.delay_ms)
                  00002530    00000016     timer.o (.text.timerA_init)
                  00002546    00000016     timer.o (.text.timerB_init)
                  0000255c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00002572    00000014     tft180.o (.text.DL_GPIO_clearPins)
                  00002586    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000259a    00000002     --HOLE-- [fill = 0]
                  0000259c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  000025b0    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000025c4    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000025d8    00000014     debug.o (.text.DL_UART_receiveData)
                  000025ec    00000014     openmv.o (.text.DL_UART_receiveData)
                  00002600    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  00002612    00000012     timer.o (.text.DL_Timer_getPendingInterrupt)
                  00002624    00000012     debug.o (.text.DL_UART_getPendingInterrupt)
                  00002636    00000012     openmv.o (.text.DL_UART_getPendingInterrupt)
                  00002648    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  0000265a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000266c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000267e    00000002     --HOLE-- [fill = 0]
                  00002680    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  00002690    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  000026a0    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  000026b0    0000000c     timer.o (.text.get_system_time_ms)
                  000026bc    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000026c6    00000008     empty.o (.text.GROUP1_IRQHandler)
                  000026ce    00000002     --HOLE-- [fill = 0]
                  000026d0    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  000026d8    00000006     libc.a : exit.c.obj (.text:abort)
                  000026de    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000026e2    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  000026e6    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000026ea    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  000026ee    00000002     --HOLE-- [fill = 0]

.cinit     0    00002e68    00000038     
                  00002e68    0000000f     (.cinit..data.load) [load image, compression = lzss]
                  00002e77    00000001     --HOLE-- [fill = 0]
                  00002e78    0000000c     (__TI_handler_table)
                  00002e84    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00002e8c    00000010     (__TI_cinit_table)
                  00002e9c    00000004     --HOLE-- [fill = 0]

.rodata    0    000026f0    00000778     
                  000026f0    000005f0     tft180.o (.rodata.ascii_font_8x16)
                  00002ce0    00000014     ti_msp_dl_config.o (.rodata.gTIMER_12TimerConfig)
                  00002cf4    00000014     ti_msp_dl_config.o (.rodata.gTIMER_8TimerConfig)
                  00002d08    00000014     empty.o (.rodata.str1.5792233746214848027.1)
                  00002d1c    00000013     openmv.o (.rodata.str1.14055760531511630791.1)
                  00002d2f    00000012     openmv.o (.rodata.str1.10222307361326560281.1)
                  00002d41    00000012     openmv.o (.rodata.str1.10322375466862398049.1)
                  00002d53    00000012     openmv.o (.rodata.str1.10499335994202400488.1)
                  00002d65    00000012     openmv.o (.rodata.str1.11972700756869316087.1)
                  00002d77    00000012     openmv.o (.rodata.str1.12960560650680968270.1)
                  00002d89    00000012     openmv.o (.rodata.str1.12983843792890534433.1)
                  00002d9b    00000012     openmv.o (.rodata.str1.16410698957387474858.1)
                  00002dad    00000012     openmv.o (.rodata.str1.4018680016288177859.1)
                  00002dbf    00000012     openmv.o (.rodata.str1.5573925365973559781.1)
                  00002dd1    00000012     openmv.o (.rodata.str1.8871796710599046883.1)
                  00002de3    00000012     openmv.o (.rodata.str1.9558640640855864504.1)
                  00002df5    00000001     --HOLE-- [fill = 0]
                  00002df6    0000000a     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_config)
                  00002e00    0000000a     ti_msp_dl_config.o (.rodata.gTFT_SPI_config)
                  00002e0a    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00002e14    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  00002e1e    0000000a     ti_msp_dl_config.o (.rodata.gUART_3Config)
                  00002e28    0000000a     openmv.o (.rodata.str1.16395811435273266920.1)
                  00002e32    0000000a     openmv.o (.rodata.str1.9416414711272993270.1)
                  00002e3c    00000008     ti_msp_dl_config.o (.rodata.gPWM_6Config)
                  00002e44    00000008     ti_msp_dl_config.o (.rodata.gPWM_7Config)
                  00002e4c    00000003     ti_msp_dl_config.o (.rodata.gPWM_6ClockConfig)
                  00002e4f    00000003     ti_msp_dl_config.o (.rodata.gPWM_7ClockConfig)
                  00002e52    00000003     ti_msp_dl_config.o (.rodata.gTIMER_12ClockConfig)
                  00002e55    00000003     ti_msp_dl_config.o (.rodata.gTIMER_8ClockConfig)
                  00002e58    00000003     openmv.o (.rodata.str1.15289475315984735280.1)
                  00002e5b    00000002     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_clockConfig)
                  00002e5d    00000002     ti_msp_dl_config.o (.rodata.gTFT_SPI_clockConfig)
                  00002e5f    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00002e61    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  00002e63    00000002     ti_msp_dl_config.o (.rodata.gUART_3ClockConfig)
                  00002e65    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001d5     UNINITIALIZED
                  20200000    000000a0     (.common:gPWM_6Backup)
                  202000a0    000000a0     (.common:gPWM_7Backup)
                  20200140    00000030     (.common:gUART_3Backup)
                  20200170    00000028     (.common:gSPI_IMU660RBBackup)
                  20200198    00000028     (.common:gTFT_SPIBackup)
                  202001c0    0000000c     (.common:openmvData)
                  202001cc    00000008     openmv.o (.bss.rx_buffer)
                  202001d4    00000001     openmv.o (.bss.data)

.data      0    202001d8    00000013     UNINITIALIZED
                  202001d8    00000004     timer.o (.data.system_time_ms)
                  202001dc    00000002     encoder.o (.data.left_counter)
                  202001de    00000002     encoder.o (.data.right_counter)
                  202001e0    00000002     openmv.o (.data.tft180_bgcolor)
                  202001e2    00000002     tft180.o (.data.tft180_bgcolor)
                  202001e4    00000002     openmv.o (.data.tft180_pencolor)
                  202001e6    00000001     openmv.o (.data.n)
                  202001e7    00000001     openmv.o (.data.state)
                  202001e8    00000001     tft180.o (.data.tft180_x_max)
                  202001e9    00000001     tft180.o (.data.tft180_y_max)
                  202001ea    00000001     debug.o (.data.uart_data)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             2814   128       448    
       startup_mspm0g350x_ticlang.o   8      192       0      
       empty.o                        104    20        0      
    +--+------------------------------+------+---------+---------+
       Total:                         2926   340       448    
                                                              
    .\drivers\
       tft180.o                       2240   1520      4      
       openmv.o                       1218   240       27     
       encoder.o                      598    0         4      
       timer.o                        230    0         4      
    +--+------------------------------+------+---------+---------+
       Total:                         4286   1760      39     
                                                              
    .\soft\
       delay.o                        138    0         0      
       debug.o                        78     0         1      
    +--+------------------------------+------+---------+---------+
       Total:                         216    0         1      
                                                              
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588    0         0      
       dl_uart.o                      90     0         0      
       dl_spi.o                       86     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         774    0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         292    0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       comparedf2.c.obj               220    0         0      
       addsf3.S.obj                   216    0         0      
       mulsf3.S.obj                   140    0         0      
       comparesf2.S.obj               118    0         0      
       aeabi_dcmp.S.obj               98     0         0      
       aeabi_fcmp.S.obj               98     0         0      
       aeabi_idivmod.S.obj            86     0         0      
       extendsfdf2.S.obj              64     0         0      
       floatsisf.S.obj                60     0         0      
       muldsi3.S.obj                  58     0         0      
       fixsfsi.S.obj                  56     0         0      
       floatunsisf.S.obj              40     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1264   0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      51        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   9762   2151      1000   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00002e8c records: 2, size/record: 8, table size: 16
	.data: load addr=00002e68, load size=0000000f bytes, run addr=202001d8, run size=00000013 bytes, compression=lzss
	.bss: load addr=00002e84, load size=00000008 bytes, run addr=20200000, run size=000001d5 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00002e78 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
000026df  ADC0_IRQHandler                 
000026df  ADC1_IRQHandler                 
000026df  AES_IRQHandler                  
000026e2  C$$EXIT                         
000026df  CANFD0_IRQHandler               
000026df  DAC0_IRQHandler                 
000026bd  DL_Common_delayCycles           
00001b5d  DL_SPI_init                     
00002601  DL_SPI_setClockConfig           
00000e69  DL_Timer_initFourCCPWMMode      
00000f6d  DL_Timer_initTimerMode          
000022a1  DL_Timer_setCaptCompUpdateMethod
00002479  DL_Timer_setCaptureCompareOutCtl
00002691  DL_Timer_setCaptureCompareValue 
000022bd  DL_Timer_setClockConfig         
00001a85  DL_UART_init                    
00002649  DL_UART_setClockConfig          
000026df  DMA_IRQHandler                  
000026df  Default_Handler                 
000026df  GROUP0_IRQHandler               
000026c7  GROUP1_IRQHandler               
000026e3  HOSTexit                        
000026df  HardFault_Handler               
000026df  I2C0_IRQHandler                 
000026df  I2C1_IRQHandler                 
000026df  NMI_Handler                     
000026df  PendSV_Handler                  
000026df  RTC_IRQHandler                  
000026e7  Reset_Handler                   
000026df  SPI0_IRQHandler                 
000026df  SPI1_IRQHandler                 
000026df  SVC_Handler                     
00000759  SYSCFG_DL_GPIO_init             
00001201  SYSCFG_DL_PWM_6_init            
0000128d  SYSCFG_DL_PWM_7_init            
00001be5  SYSCFG_DL_SPI_IMU660RB_init     
00002179  SYSCFG_DL_SYSCTL_init           
000026a1  SYSCFG_DL_SYSTICK_init          
00001c25  SYSCFG_DL_TFT_SPI_init          
00001d21  SYSCFG_DL_TIMER_12_init         
00001c65  SYSCFG_DL_TIMER_8_init          
00001acd  SYSCFG_DL_UART_0_init           
00001995  SYSCFG_DL_UART_1_init           
00001b15  SYSCFG_DL_UART_3_init           
000016e9  SYSCFG_DL_init                  
0000112d  SYSCFG_DL_initPower             
000026df  SysTick_Handler                 
000026df  TIMA0_IRQHandler                
000026df  TIMA1_IRQHandler                
000026df  TIMG0_IRQHandler                
000022f5  TIMG12_IRQHandler               
000026df  TIMG6_IRQHandler                
000026df  TIMG7_IRQHandler                
000020b5  TIMG8_IRQHandler                
0000265b  TI_memcpy_small                 
000020dd  UART0_IRQHandler                
000026df  UART1_IRQHandler                
000026df  UART2_IRQHandler                
00000c31  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00002e8c  __TI_CINIT_Base                 
00002e9c  __TI_CINIT_Limit                
00002e9c  __TI_CINIT_Warm                 
00002e78  __TI_Handler_Table_Base         
00002e84  __TI_Handler_Table_Limit        
00001dd5  __TI_auto_init_nobinit_nopinit  
00001429  __TI_decompress_lzss            
0000266d  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
0000255d  __TI_zero_init_nomemset         
0000105f  __addsf3                        
000017b9  __aeabi_dcmpeq                  
000017f5  __aeabi_dcmpge                  
00001809  __aeabi_dcmpgt                  
000017e1  __aeabi_dcmple                  
000017cd  __aeabi_dcmplt                  
00001ca5  __aeabi_f2d                     
00001e89  __aeabi_f2iz                    
0000105f  __aeabi_fadd                    
0000181d  __aeabi_fcmpeq                  
00001859  __aeabi_fcmpge                  
0000186d  __aeabi_fcmpgt                  
00001845  __aeabi_fcmple                  
00001831  __aeabi_fcmplt                  
00001319  __aeabi_fmul                    
00001055  __aeabi_fsub                    
00001d5d  __aeabi_i2f                     
0000193d  __aeabi_idiv                    
0000181b  __aeabi_idiv0                   
0000193d  __aeabi_idivmod                 
000026d1  __aeabi_memcpy                  
000026d1  __aeabi_memcpy4                 
000026d1  __aeabi_memcpy8                 
00002105  __aeabi_ui2f                    
ffffffff  __binit__                       
00001751  __cmpdf2                        
00001e11  __cmpsf2                        
00001751  __eqdf2                         
00001e11  __eqsf2                         
00001ca5  __extendsfdf2                   
00001e89  __fixsfsi                       
00001d5d  __floatsisf                     
00002105  __floatunsisf                   
00001595  __gedf2                         
00001d99  __gesf2                         
00001595  __gtdf2                         
00001d99  __gtsf2                         
00001751  __ledf2                         
00001e11  __lesf2                         
00001751  __ltdf2                         
00001e11  __ltsf2                         
UNDEFED   __mpu_init                      
00001e4d  __muldsi3                       
00001319  __mulsf3                        
00001751  __nedf2                         
00001e11  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00001055  __subsf3                        
0000212d  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
000026eb  _system_pre_init                
000026d9  abort                           
000026f0  ascii_font_8x16                 
ffffffff  binit                           
0000251b  delay_ms                        
00001609  delay_us                        
00000349  encoder_exti_callback           
00000945  func_float_to_str               
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
20200140  gUART_3Backup                   
000026b1  get_system_time_ms              
00000000  interruptVectors                
202001dc  left_counter                    
000018e1  main                            
202001c0  openmvData                      
00001ef5  openmv_analysis                 
00000555  openmv_display_data             
000014a5  openmv_init                     
000019e9  openmv_is_data_valid            
202001de  right_counter                   
0000151d  tft180_clear_color              
000000c1  tft180_init                     
00000b01  tft180_show_char_color          
00000d5d  tft180_show_num_color           
0000187f  tft180_show_string_color        
00001ba1  tft180_write_16bit_data         
00002039  tft180_write_8bit_data          
00001993  timerA_callback                 
00002531  timerA_init                     
00001e4b  timerB_callback                 
00002547  timerB_init                     
202001ea  uart_data                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  tft180_init                     
00000200  __STACK_SIZE                    
00000349  encoder_exti_callback           
00000555  openmv_display_data             
00000759  SYSCFG_DL_GPIO_init             
00000945  func_float_to_str               
00000b01  tft180_show_char_color          
00000c31  UART3_IRQHandler                
00000d5d  tft180_show_num_color           
00000e69  DL_Timer_initFourCCPWMMode      
00000f6d  DL_Timer_initTimerMode          
00001055  __aeabi_fsub                    
00001055  __subsf3                        
0000105f  __addsf3                        
0000105f  __aeabi_fadd                    
0000112d  SYSCFG_DL_initPower             
00001201  SYSCFG_DL_PWM_6_init            
0000128d  SYSCFG_DL_PWM_7_init            
00001319  __aeabi_fmul                    
00001319  __mulsf3                        
00001429  __TI_decompress_lzss            
000014a5  openmv_init                     
0000151d  tft180_clear_color              
00001595  __gedf2                         
00001595  __gtdf2                         
00001609  delay_us                        
000016e9  SYSCFG_DL_init                  
00001751  __cmpdf2                        
00001751  __eqdf2                         
00001751  __ledf2                         
00001751  __ltdf2                         
00001751  __nedf2                         
000017b9  __aeabi_dcmpeq                  
000017cd  __aeabi_dcmplt                  
000017e1  __aeabi_dcmple                  
000017f5  __aeabi_dcmpge                  
00001809  __aeabi_dcmpgt                  
0000181b  __aeabi_idiv0                   
0000181d  __aeabi_fcmpeq                  
00001831  __aeabi_fcmplt                  
00001845  __aeabi_fcmple                  
00001859  __aeabi_fcmpge                  
0000186d  __aeabi_fcmpgt                  
0000187f  tft180_show_string_color        
000018e1  main                            
0000193d  __aeabi_idiv                    
0000193d  __aeabi_idivmod                 
00001993  timerA_callback                 
00001995  SYSCFG_DL_UART_1_init           
000019e9  openmv_is_data_valid            
00001a85  DL_UART_init                    
00001acd  SYSCFG_DL_UART_0_init           
00001b15  SYSCFG_DL_UART_3_init           
00001b5d  DL_SPI_init                     
00001ba1  tft180_write_16bit_data         
00001be5  SYSCFG_DL_SPI_IMU660RB_init     
00001c25  SYSCFG_DL_TFT_SPI_init          
00001c65  SYSCFG_DL_TIMER_8_init          
00001ca5  __aeabi_f2d                     
00001ca5  __extendsfdf2                   
00001d21  SYSCFG_DL_TIMER_12_init         
00001d5d  __aeabi_i2f                     
00001d5d  __floatsisf                     
00001d99  __gesf2                         
00001d99  __gtsf2                         
00001dd5  __TI_auto_init_nobinit_nopinit  
00001e11  __cmpsf2                        
00001e11  __eqsf2                         
00001e11  __lesf2                         
00001e11  __ltsf2                         
00001e11  __nesf2                         
00001e4b  timerB_callback                 
00001e4d  __muldsi3                       
00001e89  __aeabi_f2iz                    
00001e89  __fixsfsi                       
00001ef5  openmv_analysis                 
00002039  tft180_write_8bit_data          
000020b5  TIMG8_IRQHandler                
000020dd  UART0_IRQHandler                
00002105  __aeabi_ui2f                    
00002105  __floatunsisf                   
0000212d  _c_int00_noargs                 
00002179  SYSCFG_DL_SYSCTL_init           
000022a1  DL_Timer_setCaptCompUpdateMethod
000022bd  DL_Timer_setClockConfig         
000022f5  TIMG12_IRQHandler               
00002479  DL_Timer_setCaptureCompareOutCtl
0000251b  delay_ms                        
00002531  timerA_init                     
00002547  timerB_init                     
0000255d  __TI_zero_init_nomemset         
00002601  DL_SPI_setClockConfig           
00002649  DL_UART_setClockConfig          
0000265b  TI_memcpy_small                 
0000266d  __TI_decompress_none            
00002691  DL_Timer_setCaptureCompareValue 
000026a1  SYSCFG_DL_SYSTICK_init          
000026b1  get_system_time_ms              
000026bd  DL_Common_delayCycles           
000026c7  GROUP1_IRQHandler               
000026d1  __aeabi_memcpy                  
000026d1  __aeabi_memcpy4                 
000026d1  __aeabi_memcpy8                 
000026d9  abort                           
000026df  ADC0_IRQHandler                 
000026df  ADC1_IRQHandler                 
000026df  AES_IRQHandler                  
000026df  CANFD0_IRQHandler               
000026df  DAC0_IRQHandler                 
000026df  DMA_IRQHandler                  
000026df  Default_Handler                 
000026df  GROUP0_IRQHandler               
000026df  HardFault_Handler               
000026df  I2C0_IRQHandler                 
000026df  I2C1_IRQHandler                 
000026df  NMI_Handler                     
000026df  PendSV_Handler                  
000026df  RTC_IRQHandler                  
000026df  SPI0_IRQHandler                 
000026df  SPI1_IRQHandler                 
000026df  SVC_Handler                     
000026df  SysTick_Handler                 
000026df  TIMA0_IRQHandler                
000026df  TIMA1_IRQHandler                
000026df  TIMG0_IRQHandler                
000026df  TIMG6_IRQHandler                
000026df  TIMG7_IRQHandler                
000026df  UART1_IRQHandler                
000026df  UART2_IRQHandler                
000026e2  C$$EXIT                         
000026e3  HOSTexit                        
000026e7  Reset_Handler                   
000026eb  _system_pre_init                
000026f0  ascii_font_8x16                 
00002e78  __TI_Handler_Table_Base         
00002e84  __TI_Handler_Table_Limit        
00002e8c  __TI_CINIT_Base                 
00002e9c  __TI_CINIT_Limit                
00002e9c  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200140  gUART_3Backup                   
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
202001c0  openmvData                      
202001dc  left_counter                    
202001de  right_counter                   
202001ea  uart_data                       
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[172 symbols]
