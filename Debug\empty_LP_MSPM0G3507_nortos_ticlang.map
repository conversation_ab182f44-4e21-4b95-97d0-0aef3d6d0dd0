******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 11:50:52 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002625


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00003438  00004bc8  R  X
  SRAM                  20200000   00004000  00000405  00003bfb  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00003438   00003438    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002b28   00002b28    r-x .text
  00002be8    00002be8    00000818   00000818    r-- .rodata
  00003400    00003400    00000038   00000038    r-- .cinit
20200000    20200000    00000208   00000000    rw-
  20200000    20200000    000001ed   00000000    rw- .bss
  202001f0    202001f0    00000018   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002b28     
                  000000c0    0000028c     empty.o (.text.main)
                  0000034c    00000288     tft180.o (.text.tft180_init)
                  000005d4    0000020c     encoder.o (.text.encoder_exti_callback)
                  000007e0    00000204     openmv.o (.text.openmv_display_data)
                  000009e4    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000bd0    000001bc     tft180.o (.text.func_float_to_str)
                  00000d8c    00000130     tft180.o (.text.tft180_show_char_color)
                  00000ebc    0000012c     openmv.o (.text.UART3_IRQHandler)
                  00000fe8    0000010c     tft180.o (.text.tft180_show_num_color)
                  000010f4    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000011f8    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000012e0    000000d8     servo.o (.text.servo_set_angle)
                  000013b8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001490    000000d4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001564    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_6_init)
                  000015f0    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_7_init)
                  0000167c    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00001708    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  0000178c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  0000180e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001810    00000080     servo.o (.text.servo_config_type)
                  00001890    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  0000190c    00000078     openmv.o (.text.openmv_init)
                  00001984    00000078     tft180.o (.text.tft180_clear_color)
                  000019fc    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001a70    00000074     delay.o (.text.delay_us)
                  00001ae4    0000006c     tft180.o (.text.tft180_set_region)
                  00001b50    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001bb8    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001c20    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001c82    00000002     empty.o (.text.timerA_callback)
                  00001c84    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00001ce6    00000062     tft180.o (.text.tft180_show_string_color)
                  00001d48    00000060     servo.o (.text.servo_init)
                  00001da8    00000058     openmv.o (.text.openmv_reset_uart_state)
                  00001e00    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00001e56    00000002     empty.o (.text.timerB_callback)
                  00001e58    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  00001eac    00000050     openmv.o (.text.openmv_is_data_valid)
                  00001efc    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00001f48    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001f90    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00001fd8    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_3_init)
                  00002020    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00002064    00000044     tft180.o (.text.tft180_write_16bit_data)
                  000020a8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_IMU660RB_init)
                  000020e8    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TFT_SPI_init)
                  00002128    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_8_init)
                  00002168    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000021a8    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  000021e4    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_12_init)
                  00002220    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  0000225c    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00002298    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000022d4    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000230e    00000002     --HOLE-- [fill = 0]
                  00002310    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  0000234a    00000002     --HOLE-- [fill = 0]
                  0000234c    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  00002384    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000023b8    00000034     openmv.o (.text.openmv_analysis)
                  000023ec    00000034     servo.o (.text.servo_get_angle)
                  00002420    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  00002450    00000030     tft180.o (.text.tft180_write_index)
                  00002480    0000002c     openmv.o (.text.__NVIC_ClearPendingIRQ)
                  000024ac    0000002c     timer.o (.text.__NVIC_ClearPendingIRQ)
                  000024d8    0000002c     openmv.o (.text.__NVIC_EnableIRQ)
                  00002504    0000002c     timer.o (.text.__NVIC_EnableIRQ)
                  00002530    0000002c     tft180.o (.text.tft180_write_8bit_data)
                  0000255c    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00002584    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  000025ac    00000028     timer.o (.text.TIMG8_IRQHandler)
                  000025d4    00000028     debug.o (.text.UART0_IRQHandler)
                  000025fc    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00002624    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0000264c    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  00002670    00000022     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00002692    00000002     --HOLE-- [fill = 0]
                  00002694    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000026b4    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000026d2    00000002     --HOLE-- [fill = 0]
                  000026d4    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  000026f0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  0000270c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00002728    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002744    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00002760    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  0000277c    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00002798    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000027b4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000027d0    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  000027ec    0000001c     timer.o (.text.TIMG12_IRQHandler)
                  00002808    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00002820    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002838    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  00002850    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002868    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00002880    00000018     tft180.o (.text.DL_GPIO_setPins)
                  00002898    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000028b0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000028c8    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  000028e0    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  000028f8    00000018     tft180.o (.text.DL_SPI_isBusy)
                  00002910    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  00002928    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00002940    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002958    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00002970    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00002988    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000029a0    00000018     openmv.o (.text.DL_UART_isRXFIFOEmpty)
                  000029b8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  000029d0    00000016     encoder.o (.text.DL_GPIO_readPins)
                  000029e6    00000016     tft180.o (.text.DL_SPI_transmitData8)
                  000029fc    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00002a12    00000016     delay.o (.text.delay_ms)
                  00002a28    00000016     timer.o (.text.timerA_init)
                  00002a3e    00000016     timer.o (.text.timerB_init)
                  00002a54    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00002a6a    00000014     tft180.o (.text.DL_GPIO_clearPins)
                  00002a7e    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00002a92    00000002     --HOLE-- [fill = 0]
                  00002a94    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  00002aa8    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00002abc    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00002ad0    00000014     debug.o (.text.DL_UART_receiveData)
                  00002ae4    00000014     openmv.o (.text.DL_UART_receiveData)
                  00002af8    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  00002b0a    00000012     timer.o (.text.DL_Timer_getPendingInterrupt)
                  00002b1c    00000012     debug.o (.text.DL_UART_getPendingInterrupt)
                  00002b2e    00000012     openmv.o (.text.DL_UART_getPendingInterrupt)
                  00002b40    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00002b52    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00002b64    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00002b76    00000002     --HOLE-- [fill = 0]
                  00002b78    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  00002b88    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00002b98    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00002ba8    0000000c     timer.o (.text.get_system_time_ms)
                  00002bb4    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00002bbe    00000008     empty.o (.text.GROUP1_IRQHandler)
                  00002bc6    00000002     --HOLE-- [fill = 0]
                  00002bc8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00002bd0    00000006     libc.a : exit.c.obj (.text:abort)
                  00002bd6    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002bda    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00002bde    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00002be2    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00002be6    00000002     --HOLE-- [fill = 0]

.cinit     0    00003400    00000038     
                  00003400    00000010     (.cinit..data.load) [load image, compression = lzss]
                  00003410    0000000c     (__TI_handler_table)
                  0000341c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00003424    00000010     (__TI_cinit_table)
                  00003434    00000004     --HOLE-- [fill = 0]

.rodata    0    00002be8    00000818     
                  00002be8    000005f0     tft180.o (.rodata.ascii_font_8x16)
                  000031d8    00000016     empty.o (.rodata.str1.4000995719088696555.1)
                  000031ee    00000015     empty.o (.rodata.str1.7079713434352825882.1)
                  00003203    00000001     --HOLE-- [fill = 0]
                  00003204    00000014     ti_msp_dl_config.o (.rodata.gTIMER_12TimerConfig)
                  00003218    00000014     ti_msp_dl_config.o (.rodata.gTIMER_8TimerConfig)
                  0000322c    00000014     empty.o (.rodata.str1.5792233746214848027.1)
                  00003240    00000013     openmv.o (.rodata.str1.14055760531511630791.1)
                  00003253    00000013     empty.o (.rodata.str1.15930989295766594416.1)
                  00003266    00000012     empty.o (.rodata.str1.101204432782223354.1)
                  00003278    00000012     openmv.o (.rodata.str1.10222307361326560281.1)
                  0000328a    00000012     openmv.o (.rodata.str1.10322375466862398049.1)
                  0000329c    00000012     openmv.o (.rodata.str1.10499335994202400488.1)
                  000032ae    00000012     openmv.o (.rodata.str1.11972700756869316087.1)
                  000032c0    00000012     openmv.o (.rodata.str1.12960560650680968270.1)
                  000032d2    00000012     openmv.o (.rodata.str1.12983843792890534433.1)
                  000032e4    00000012     empty.o (.rodata.str1.15486159998237592746.1)
                  000032f6    00000012     openmv.o (.rodata.str1.16410698957387474858.1)
                  00003308    00000012     openmv.o (.rodata.str1.4018680016288177859.1)
                  0000331a    00000012     empty.o (.rodata.str1.5100843677217179155.1)
                  0000332c    00000012     openmv.o (.rodata.str1.5573925365973559781.1)
                  0000333e    00000012     empty.o (.rodata.str1.6969777895189110550.1)
                  00003350    00000012     openmv.o (.rodata.str1.8871796710599046883.1)
                  00003362    00000012     openmv.o (.rodata.str1.9558640640855864504.1)
                  00003374    0000000b     empty.o (.rodata.str1.17102900359786703537.1)
                  0000337f    00000001     --HOLE-- [fill = 0]
                  00003380    0000000a     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_config)
                  0000338a    0000000a     ti_msp_dl_config.o (.rodata.gTFT_SPI_config)
                  00003394    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  0000339e    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  000033a8    0000000a     ti_msp_dl_config.o (.rodata.gUART_3Config)
                  000033b2    0000000a     openmv.o (.rodata.str1.16395811435273266920.1)
                  000033bc    0000000a     openmv.o (.rodata.str1.9416414711272993270.1)
                  000033c6    00000002     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_clockConfig)
                  000033c8    00000008     ti_msp_dl_config.o (.rodata.gPWM_6Config)
                  000033d0    00000008     ti_msp_dl_config.o (.rodata.gPWM_7Config)
                  000033d8    00000007     empty.o (.rodata.str1.10059295903439925133.1)
                  000033df    00000007     empty.o (.rodata.str1.18230774644621384456.1)
                  000033e6    00000003     ti_msp_dl_config.o (.rodata.gPWM_6ClockConfig)
                  000033e9    00000003     ti_msp_dl_config.o (.rodata.gPWM_7ClockConfig)
                  000033ec    00000003     ti_msp_dl_config.o (.rodata.gTIMER_12ClockConfig)
                  000033ef    00000003     ti_msp_dl_config.o (.rodata.gTIMER_8ClockConfig)
                  000033f2    00000003     openmv.o (.rodata.str1.15289475315984735280.1)
                  000033f5    00000002     ti_msp_dl_config.o (.rodata.gTFT_SPI_clockConfig)
                  000033f7    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  000033f9    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  000033fb    00000002     ti_msp_dl_config.o (.rodata.gUART_3ClockConfig)
                  000033fd    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001ed     UNINITIALIZED
                  20200000    000000a0     (.common:gPWM_6Backup)
                  202000a0    000000a0     (.common:gPWM_7Backup)
                  20200140    00000030     (.common:gUART_3Backup)
                  20200170    00000028     (.common:gSPI_IMU660RBBackup)
                  20200198    00000028     (.common:gTFT_SPIBackup)
                  202001c0    00000018     servo.o (.bss.servo_configs)
                  202001d8    0000000c     (.common:openmvData)
                  202001e4    00000008     openmv.o (.bss.rx_buffer)
                  202001ec    00000001     openmv.o (.bss.data)

.data      0    202001f0    00000018     UNINITIALIZED
                  202001f0    00000004     timer.o (.data.system_time_ms)
                  202001f4    00000002     encoder.o (.data.left_counter)
                  202001f6    00000002     encoder.o (.data.right_counter)
                  202001f8    00000002     empty.o (.data.tft180_bgcolor)
                  202001fa    00000002     openmv.o (.data.tft180_bgcolor)
                  202001fc    00000002     tft180.o (.data.tft180_bgcolor)
                  202001fe    00000002     empty.o (.data.tft180_pencolor)
                  20200200    00000002     openmv.o (.data.tft180_pencolor)
                  20200202    00000001     empty.o (.data.main.test_step)
                  20200203    00000001     openmv.o (.data.n)
                  20200204    00000001     openmv.o (.data.state)
                  20200205    00000001     tft180.o (.data.tft180_x_max)
                  20200206    00000001     tft180.o (.data.tft180_y_max)
                  20200207    00000001     debug.o (.data.uart_data)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2814    128       448    
       empty.o                        664     179       5      
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3486    499       453    
                                                               
    .\drivers\
       tft180.o                       2240    1520      4      
       openmv.o                       1306    240       27     
       encoder.o                      598     0         4      
       servo.o                        492     0         24     
       timer.o                        230     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         4866    1760      63     
                                                               
    .\soft\
       delay.o                        138     0         0      
       debug.o                        78      0         1      
    +--+------------------------------+-------+---------+---------+
       Total:                         216     0         1      
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_uart.o                      90      0         0      
       dl_spi.o                       86      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         774     0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         292     0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatunsisf.S.obj              40      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1394    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       52        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   11032   2311      1029   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00003424 records: 2, size/record: 8, table size: 16
	.data: load addr=00003400, load size=00000010 bytes, run addr=202001f0, run size=00000018 bytes, compression=lzss
	.bss: load addr=0000341c, load size=00000008 bytes, run addr=20200000, run size=000001ed bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00003410 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00002bd7  ADC0_IRQHandler                 
00002bd7  ADC1_IRQHandler                 
00002bd7  AES_IRQHandler                  
00002bda  C$$EXIT                         
00002bd7  CANFD0_IRQHandler               
00002bd7  DAC0_IRQHandler                 
00002bb5  DL_Common_delayCycles           
00002021  DL_SPI_init                     
00002af9  DL_SPI_setClockConfig           
000010f5  DL_Timer_initFourCCPWMMode      
000011f9  DL_Timer_initTimerMode          
00002799  DL_Timer_setCaptCompUpdateMethod
00002971  DL_Timer_setCaptureCompareOutCtl
00002b89  DL_Timer_setCaptureCompareValue 
000027b5  DL_Timer_setClockConfig         
00001f49  DL_UART_init                    
00002b41  DL_UART_setClockConfig          
00002bd7  DMA_IRQHandler                  
00002bd7  Default_Handler                 
00002bd7  GROUP0_IRQHandler               
00002bbf  GROUP1_IRQHandler               
00002bdb  HOSTexit                        
00002bd7  HardFault_Handler               
00002bd7  I2C0_IRQHandler                 
00002bd7  I2C1_IRQHandler                 
00002bd7  NMI_Handler                     
00002bd7  PendSV_Handler                  
00002bd7  RTC_IRQHandler                  
00002bdf  Reset_Handler                   
00002bd7  SPI0_IRQHandler                 
00002bd7  SPI1_IRQHandler                 
00002bd7  SVC_Handler                     
000009e5  SYSCFG_DL_GPIO_init             
00001565  SYSCFG_DL_PWM_6_init            
000015f1  SYSCFG_DL_PWM_7_init            
000020a9  SYSCFG_DL_SPI_IMU660RB_init     
00002671  SYSCFG_DL_SYSCTL_init           
00002b99  SYSCFG_DL_SYSTICK_init          
000020e9  SYSCFG_DL_TFT_SPI_init          
000021e5  SYSCFG_DL_TIMER_12_init         
00002129  SYSCFG_DL_TIMER_8_init          
00001f91  SYSCFG_DL_UART_0_init           
00001e59  SYSCFG_DL_UART_1_init           
00001fd9  SYSCFG_DL_UART_3_init           
00001b51  SYSCFG_DL_init                  
00001491  SYSCFG_DL_initPower             
00002bd7  SysTick_Handler                 
00002bd7  TIMA0_IRQHandler                
00002bd7  TIMA1_IRQHandler                
00002bd7  TIMG0_IRQHandler                
000027ed  TIMG12_IRQHandler               
00002bd7  TIMG6_IRQHandler                
00002bd7  TIMG7_IRQHandler                
000025ad  TIMG8_IRQHandler                
00002b53  TI_memcpy_small                 
000025d5  UART0_IRQHandler                
00002bd7  UART1_IRQHandler                
00002bd7  UART2_IRQHandler                
00000ebd  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00003424  __TI_CINIT_Base                 
00003434  __TI_CINIT_Limit                
00003434  __TI_CINIT_Warm                 
00003410  __TI_Handler_Table_Base         
0000341c  __TI_Handler_Table_Limit        
00002299  __TI_auto_init_nobinit_nopinit  
00001891  __TI_decompress_lzss            
00002b65  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00002a55  __TI_zero_init_nomemset         
000013c3  __addsf3                        
00001c21  __aeabi_dcmpeq                  
00001c5d  __aeabi_dcmpge                  
00001c71  __aeabi_dcmpgt                  
00001c49  __aeabi_dcmple                  
00001c35  __aeabi_dcmplt                  
00002169  __aeabi_f2d                     
0000234d  __aeabi_f2iz                    
000013c3  __aeabi_fadd                    
00001c85  __aeabi_fcmpeq                  
00001cc1  __aeabi_fcmpge                  
00001cd5  __aeabi_fcmpgt                  
00001cad  __aeabi_fcmple                  
00001c99  __aeabi_fcmplt                  
0000178d  __aeabi_fdiv                    
0000167d  __aeabi_fmul                    
000013b9  __aeabi_fsub                    
00002221  __aeabi_i2f                     
00001e01  __aeabi_idiv                    
0000180f  __aeabi_idiv0                   
00001e01  __aeabi_idivmod                 
00002bc9  __aeabi_memcpy                  
00002bc9  __aeabi_memcpy4                 
00002bc9  __aeabi_memcpy8                 
000025fd  __aeabi_ui2f                    
ffffffff  __binit__                       
00001bb9  __cmpdf2                        
000022d5  __cmpsf2                        
0000178d  __divsf3                        
00001bb9  __eqdf2                         
000022d5  __eqsf2                         
00002169  __extendsfdf2                   
0000234d  __fixsfsi                       
00002221  __floatsisf                     
000025fd  __floatunsisf                   
000019fd  __gedf2                         
0000225d  __gesf2                         
000019fd  __gtdf2                         
0000225d  __gtsf2                         
00001bb9  __ledf2                         
000022d5  __lesf2                         
00001bb9  __ltdf2                         
000022d5  __ltsf2                         
UNDEFED   __mpu_init                      
00002311  __muldsi3                       
0000167d  __mulsf3                        
00001bb9  __nedf2                         
000022d5  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000013b9  __subsf3                        
00002625  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00002be3  _system_pre_init                
00002bd1  abort                           
00002be8  ascii_font_8x16                 
ffffffff  binit                           
00002a13  delay_ms                        
00001a71  delay_us                        
000005d5  encoder_exti_callback           
00000bd1  func_float_to_str               
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
20200140  gUART_3Backup                   
00002ba9  get_system_time_ms              
00000000  interruptVectors                
202001f4  left_counter                    
000000c1  main                            
202001d8  openmvData                      
000023b9  openmv_analysis                 
000007e1  openmv_display_data             
0000190d  openmv_init                     
00001ead  openmv_is_data_valid            
00001da9  openmv_reset_uart_state         
202001f6  right_counter                   
00001811  servo_config_type               
000023ed  servo_get_angle                 
00001d49  servo_init                      
000012e1  servo_set_angle                 
00001985  tft180_clear_color              
0000034d  tft180_init                     
00000d8d  tft180_show_char_color          
00000fe9  tft180_show_num_color           
00001ce7  tft180_show_string_color        
00002065  tft180_write_16bit_data         
00002531  tft180_write_8bit_data          
00001c83  timerA_callback                 
00002a29  timerA_init                     
00001e57  timerB_callback                 
00002a3f  timerB_init                     
20200207  uart_data                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  main                            
00000200  __STACK_SIZE                    
0000034d  tft180_init                     
000005d5  encoder_exti_callback           
000007e1  openmv_display_data             
000009e5  SYSCFG_DL_GPIO_init             
00000bd1  func_float_to_str               
00000d8d  tft180_show_char_color          
00000ebd  UART3_IRQHandler                
00000fe9  tft180_show_num_color           
000010f5  DL_Timer_initFourCCPWMMode      
000011f9  DL_Timer_initTimerMode          
000012e1  servo_set_angle                 
000013b9  __aeabi_fsub                    
000013b9  __subsf3                        
000013c3  __addsf3                        
000013c3  __aeabi_fadd                    
00001491  SYSCFG_DL_initPower             
00001565  SYSCFG_DL_PWM_6_init            
000015f1  SYSCFG_DL_PWM_7_init            
0000167d  __aeabi_fmul                    
0000167d  __mulsf3                        
0000178d  __aeabi_fdiv                    
0000178d  __divsf3                        
0000180f  __aeabi_idiv0                   
00001811  servo_config_type               
00001891  __TI_decompress_lzss            
0000190d  openmv_init                     
00001985  tft180_clear_color              
000019fd  __gedf2                         
000019fd  __gtdf2                         
00001a71  delay_us                        
00001b51  SYSCFG_DL_init                  
00001bb9  __cmpdf2                        
00001bb9  __eqdf2                         
00001bb9  __ledf2                         
00001bb9  __ltdf2                         
00001bb9  __nedf2                         
00001c21  __aeabi_dcmpeq                  
00001c35  __aeabi_dcmplt                  
00001c49  __aeabi_dcmple                  
00001c5d  __aeabi_dcmpge                  
00001c71  __aeabi_dcmpgt                  
00001c83  timerA_callback                 
00001c85  __aeabi_fcmpeq                  
00001c99  __aeabi_fcmplt                  
00001cad  __aeabi_fcmple                  
00001cc1  __aeabi_fcmpge                  
00001cd5  __aeabi_fcmpgt                  
00001ce7  tft180_show_string_color        
00001d49  servo_init                      
00001da9  openmv_reset_uart_state         
00001e01  __aeabi_idiv                    
00001e01  __aeabi_idivmod                 
00001e57  timerB_callback                 
00001e59  SYSCFG_DL_UART_1_init           
00001ead  openmv_is_data_valid            
00001f49  DL_UART_init                    
00001f91  SYSCFG_DL_UART_0_init           
00001fd9  SYSCFG_DL_UART_3_init           
00002021  DL_SPI_init                     
00002065  tft180_write_16bit_data         
000020a9  SYSCFG_DL_SPI_IMU660RB_init     
000020e9  SYSCFG_DL_TFT_SPI_init          
00002129  SYSCFG_DL_TIMER_8_init          
00002169  __aeabi_f2d                     
00002169  __extendsfdf2                   
000021e5  SYSCFG_DL_TIMER_12_init         
00002221  __aeabi_i2f                     
00002221  __floatsisf                     
0000225d  __gesf2                         
0000225d  __gtsf2                         
00002299  __TI_auto_init_nobinit_nopinit  
000022d5  __cmpsf2                        
000022d5  __eqsf2                         
000022d5  __lesf2                         
000022d5  __ltsf2                         
000022d5  __nesf2                         
00002311  __muldsi3                       
0000234d  __aeabi_f2iz                    
0000234d  __fixsfsi                       
000023b9  openmv_analysis                 
000023ed  servo_get_angle                 
00002531  tft180_write_8bit_data          
000025ad  TIMG8_IRQHandler                
000025d5  UART0_IRQHandler                
000025fd  __aeabi_ui2f                    
000025fd  __floatunsisf                   
00002625  _c_int00_noargs                 
00002671  SYSCFG_DL_SYSCTL_init           
00002799  DL_Timer_setCaptCompUpdateMethod
000027b5  DL_Timer_setClockConfig         
000027ed  TIMG12_IRQHandler               
00002971  DL_Timer_setCaptureCompareOutCtl
00002a13  delay_ms                        
00002a29  timerA_init                     
00002a3f  timerB_init                     
00002a55  __TI_zero_init_nomemset         
00002af9  DL_SPI_setClockConfig           
00002b41  DL_UART_setClockConfig          
00002b53  TI_memcpy_small                 
00002b65  __TI_decompress_none            
00002b89  DL_Timer_setCaptureCompareValue 
00002b99  SYSCFG_DL_SYSTICK_init          
00002ba9  get_system_time_ms              
00002bb5  DL_Common_delayCycles           
00002bbf  GROUP1_IRQHandler               
00002bc9  __aeabi_memcpy                  
00002bc9  __aeabi_memcpy4                 
00002bc9  __aeabi_memcpy8                 
00002bd1  abort                           
00002bd7  ADC0_IRQHandler                 
00002bd7  ADC1_IRQHandler                 
00002bd7  AES_IRQHandler                  
00002bd7  CANFD0_IRQHandler               
00002bd7  DAC0_IRQHandler                 
00002bd7  DMA_IRQHandler                  
00002bd7  Default_Handler                 
00002bd7  GROUP0_IRQHandler               
00002bd7  HardFault_Handler               
00002bd7  I2C0_IRQHandler                 
00002bd7  I2C1_IRQHandler                 
00002bd7  NMI_Handler                     
00002bd7  PendSV_Handler                  
00002bd7  RTC_IRQHandler                  
00002bd7  SPI0_IRQHandler                 
00002bd7  SPI1_IRQHandler                 
00002bd7  SVC_Handler                     
00002bd7  SysTick_Handler                 
00002bd7  TIMA0_IRQHandler                
00002bd7  TIMA1_IRQHandler                
00002bd7  TIMG0_IRQHandler                
00002bd7  TIMG6_IRQHandler                
00002bd7  TIMG7_IRQHandler                
00002bd7  UART1_IRQHandler                
00002bd7  UART2_IRQHandler                
00002bda  C$$EXIT                         
00002bdb  HOSTexit                        
00002bdf  Reset_Handler                   
00002be3  _system_pre_init                
00002be8  ascii_font_8x16                 
00003410  __TI_Handler_Table_Base         
0000341c  __TI_Handler_Table_Limit        
00003424  __TI_CINIT_Base                 
00003434  __TI_CINIT_Limit                
00003434  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200140  gUART_3Backup                   
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
202001d8  openmvData                      
202001f4  left_counter                    
202001f6  right_counter                   
20200207  uart_data                       
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[179 symbols]
