******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Aug  1 11:43:10 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00002641


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  00003488  00004b78  R  X
  SRAM                  20200000   00004000  00000405  00003bfb  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00003488   00003488    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00002b78   00002b78    r-x .text
  00002c38    00002c38    00000818   00000818    r-- .rodata
  00003450    00003450    00000038   00000038    r-- .cinit
20200000    20200000    00000208   00000000    rw-
  20200000    20200000    000001ed   00000000    rw- .bss
  202001f0    202001f0    00000018   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00002b78     
                  000000c0    00000290     empty.o (.text.main)
                  00000350    00000288     tft180.o (.text.tft180_init)
                  000005d8    0000020c     encoder.o (.text.encoder_exti_callback)
                  000007e4    00000204     openmv.o (.text.openmv_display_data)
                  000009e8    000001ec     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000bd4    000001bc     tft180.o (.text.func_float_to_str)
                  00000d90    00000130     tft180.o (.text.tft180_show_char_color)
                  00000ec0    0000012c     openmv.o (.text.UART3_IRQHandler)
                  00000fec    0000010c     tft180.o (.text.tft180_show_num_color)
                  000010f8    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  000011fc    000000e8                 : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000012e4    000000d8     servo.o (.text.servo_set_angle)
                  000013bc    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00001494    000000d4     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00001568    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_6_init)
                  000015f4    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_7_init)
                  00001680    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  0000170c    00000084     ti_msp_dl_config.o (.text.__NVIC_SetPriority)
                  00001790    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00001812    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00001814    00000080     servo.o (.text.servo_config_type)
                  00001894    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00001910    00000078     openmv.o (.text.openmv_init)
                  00001988    00000078     servo.o (.text.servo_init)
                  00001a00    00000078     tft180.o (.text.tft180_clear_color)
                  00001a78    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00001aec    00000074     delay.o (.text.delay_us)
                  00001b60    0000006c     tft180.o (.text.tft180_set_region)
                  00001bcc    00000068     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00001c34    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  00001c9c    00000062                            : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00001cfe    00000002     empty.o (.text.timerA_callback)
                  00001d00    00000062     libclang_rt.builtins.a : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00001d62    00000062     tft180.o (.text.tft180_show_string_color)
                  00001dc4    00000058     openmv.o (.text.openmv_reset_uart_state)
                  00001e1c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00001e72    00000002     empty.o (.text.timerB_callback)
                  00001e74    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_1_init)
                  00001ec8    00000050     openmv.o (.text.openmv_is_data_valid)
                  00001f18    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00001f64    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00001fac    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00001ff4    00000048     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_3_init)
                  0000203c    00000044     driverlib.a : dl_spi.o (.text.DL_SPI_init)
                  00002080    00000044     tft180.o (.text.tft180_write_16bit_data)
                  000020c4    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SPI_IMU660RB_init)
                  00002104    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TFT_SPI_init)
                  00002144    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_8_init)
                  00002184    00000040     libclang_rt.builtins.a : extendsfdf2.S.obj (.text.__extendsfdf2)
                  000021c4    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00002200    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_12_init)
                  0000223c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00002278    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  000022b4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000022f0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  0000232a    00000002     --HOLE-- [fill = 0]
                  0000232c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00002366    00000002     --HOLE-- [fill = 0]
                  00002368    00000038                            : fixsfsi.S.obj (.text.__fixsfsi)
                  000023a0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  000023d4    00000034     openmv.o (.text.openmv_analysis)
                  00002408    00000034     servo.o (.text.servo_get_angle)
                  0000243c    00000030     ti_msp_dl_config.o (.text.DL_SPI_setFIFOThreshold)
                  0000246c    00000030     tft180.o (.text.tft180_write_index)
                  0000249c    0000002c     openmv.o (.text.__NVIC_ClearPendingIRQ)
                  000024c8    0000002c     timer.o (.text.__NVIC_ClearPendingIRQ)
                  000024f4    0000002c     openmv.o (.text.__NVIC_EnableIRQ)
                  00002520    0000002c     timer.o (.text.__NVIC_EnableIRQ)
                  0000254c    0000002c     tft180.o (.text.tft180_write_8bit_data)
                  00002578    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000025a0    00000028     ti_msp_dl_config.o (.text.DL_SYSTICK_init)
                  000025c8    00000028     timer.o (.text.TIMG8_IRQHandler)
                  000025f0    00000028     debug.o (.text.UART0_IRQHandler)
                  00002618    00000028     libclang_rt.builtins.a : floatunsisf.S.obj (.text.__floatunsisf)
                  00002640    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00002668    00000024     ti_msp_dl_config.o (.text.DL_SPI_setBitRateSerialClockDivider)
                  0000268c    00000022     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  000026ae    00000002     --HOLE-- [fill = 0]
                  000026b0    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000026d0    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000026ee    00000002     --HOLE-- [fill = 0]
                  000026f0    0000001c     encoder.o (.text.DL_GPIO_clearInterruptStatus)
                  0000270c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00002728    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  00002744    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00002760    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  0000277c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00002798    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  000027b4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000027d0    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000027ec    0000001c     ti_msp_dl_config.o (.text.DL_UART_enableInterrupt)
                  00002808    0000001c     timer.o (.text.TIMG12_IRQHandler)
                  00002824    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  0000283c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00002854    00000018     encoder.o (.text.DL_GPIO_getEnabledInterruptStatus)
                  0000286c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00002884    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  0000289c    00000018     tft180.o (.text.DL_GPIO_setPins)
                  000028b4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000028cc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000028e4    00000018     ti_msp_dl_config.o (.text.DL_SPI_enable)
                  000028fc    00000018     ti_msp_dl_config.o (.text.DL_SPI_enablePower)
                  00002914    00000018     tft180.o (.text.DL_SPI_isBusy)
                  0000292c    00000018     ti_msp_dl_config.o (.text.DL_SPI_reset)
                  00002944    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0000295c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00002974    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  0000298c    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000029a4    00000018     servo.o (.text.DL_Timer_startCounter)
                  000029bc    00000018     servo.o (.text.DL_Timer_stopCounter)
                  000029d4    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  000029ec    00000018     openmv.o (.text.DL_UART_isRXFIFOEmpty)
                  00002a04    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00002a1c    00000016     encoder.o (.text.DL_GPIO_readPins)
                  00002a32    00000016     tft180.o (.text.DL_SPI_transmitData8)
                  00002a48    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00002a5e    00000016     delay.o (.text.delay_ms)
                  00002a74    00000016     timer.o (.text.timerA_init)
                  00002a8a    00000016     timer.o (.text.timerB_init)
                  00002aa0    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00002ab6    00000014     tft180.o (.text.DL_GPIO_clearPins)
                  00002aca    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00002ade    00000002     --HOLE-- [fill = 0]
                  00002ae0    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFCLK)
                  00002af4    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00002b08    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  00002b1c    00000014     debug.o (.text.DL_UART_receiveData)
                  00002b30    00000014     openmv.o (.text.DL_UART_receiveData)
                  00002b44    00000012     driverlib.a : dl_spi.o (.text.DL_SPI_setClockConfig)
                  00002b56    00000012     timer.o (.text.DL_Timer_getPendingInterrupt)
                  00002b68    00000012     debug.o (.text.DL_UART_getPendingInterrupt)
                  00002b7a    00000012     openmv.o (.text.DL_UART_getPendingInterrupt)
                  00002b8c    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00002b9e    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00002bb0    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00002bc2    00000002     --HOLE-- [fill = 0]
                  00002bc4    00000010     ti_msp_dl_config.o (.text.DL_SYSTICK_enable)
                  00002bd4    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00002be4    00000010     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00002bf4    0000000c     timer.o (.text.get_system_time_ms)
                  00002c00    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00002c0a    00000008     empty.o (.text.GROUP1_IRQHandler)
                  00002c12    00000002     --HOLE-- [fill = 0]
                  00002c14    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00002c1c    00000006     libc.a : exit.c.obj (.text:abort)
                  00002c22    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00002c26    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00002c2a    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00002c2e    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00002c32    00000006     --HOLE-- [fill = 0]

.cinit     0    00003450    00000038     
                  00003450    00000010     (.cinit..data.load) [load image, compression = lzss]
                  00003460    0000000c     (__TI_handler_table)
                  0000346c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00003474    00000010     (__TI_cinit_table)
                  00003484    00000004     --HOLE-- [fill = 0]

.rodata    0    00002c38    00000818     
                  00002c38    000005f0     tft180.o (.rodata.ascii_font_8x16)
                  00003228    00000016     empty.o (.rodata.str1.4000995719088696555.1)
                  0000323e    00000015     empty.o (.rodata.str1.7079713434352825882.1)
                  00003253    00000001     --HOLE-- [fill = 0]
                  00003254    00000014     ti_msp_dl_config.o (.rodata.gTIMER_12TimerConfig)
                  00003268    00000014     ti_msp_dl_config.o (.rodata.gTIMER_8TimerConfig)
                  0000327c    00000014     empty.o (.rodata.str1.5792233746214848027.1)
                  00003290    00000013     openmv.o (.rodata.str1.14055760531511630791.1)
                  000032a3    00000013     empty.o (.rodata.str1.15930989295766594416.1)
                  000032b6    00000012     empty.o (.rodata.str1.101204432782223354.1)
                  000032c8    00000012     openmv.o (.rodata.str1.10222307361326560281.1)
                  000032da    00000012     openmv.o (.rodata.str1.10322375466862398049.1)
                  000032ec    00000012     openmv.o (.rodata.str1.10499335994202400488.1)
                  000032fe    00000012     openmv.o (.rodata.str1.11972700756869316087.1)
                  00003310    00000012     openmv.o (.rodata.str1.12960560650680968270.1)
                  00003322    00000012     openmv.o (.rodata.str1.12983843792890534433.1)
                  00003334    00000012     empty.o (.rodata.str1.15486159998237592746.1)
                  00003346    00000012     openmv.o (.rodata.str1.16410698957387474858.1)
                  00003358    00000012     openmv.o (.rodata.str1.4018680016288177859.1)
                  0000336a    00000012     empty.o (.rodata.str1.5100843677217179155.1)
                  0000337c    00000012     openmv.o (.rodata.str1.5573925365973559781.1)
                  0000338e    00000012     empty.o (.rodata.str1.6969777895189110550.1)
                  000033a0    00000012     openmv.o (.rodata.str1.8871796710599046883.1)
                  000033b2    00000012     openmv.o (.rodata.str1.9558640640855864504.1)
                  000033c4    0000000b     empty.o (.rodata.str1.17102900359786703537.1)
                  000033cf    00000001     --HOLE-- [fill = 0]
                  000033d0    0000000a     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_config)
                  000033da    0000000a     ti_msp_dl_config.o (.rodata.gTFT_SPI_config)
                  000033e4    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  000033ee    0000000a     ti_msp_dl_config.o (.rodata.gUART_1Config)
                  000033f8    0000000a     ti_msp_dl_config.o (.rodata.gUART_3Config)
                  00003402    0000000a     openmv.o (.rodata.str1.16395811435273266920.1)
                  0000340c    0000000a     openmv.o (.rodata.str1.9416414711272993270.1)
                  00003416    00000002     ti_msp_dl_config.o (.rodata.gSPI_IMU660RB_clockConfig)
                  00003418    00000008     ti_msp_dl_config.o (.rodata.gPWM_6Config)
                  00003420    00000008     ti_msp_dl_config.o (.rodata.gPWM_7Config)
                  00003428    00000007     empty.o (.rodata.str1.10059295903439925133.1)
                  0000342f    00000007     empty.o (.rodata.str1.18230774644621384456.1)
                  00003436    00000003     ti_msp_dl_config.o (.rodata.gPWM_6ClockConfig)
                  00003439    00000003     ti_msp_dl_config.o (.rodata.gPWM_7ClockConfig)
                  0000343c    00000003     ti_msp_dl_config.o (.rodata.gTIMER_12ClockConfig)
                  0000343f    00000003     ti_msp_dl_config.o (.rodata.gTIMER_8ClockConfig)
                  00003442    00000003     openmv.o (.rodata.str1.15289475315984735280.1)
                  00003445    00000002     ti_msp_dl_config.o (.rodata.gTFT_SPI_clockConfig)
                  00003447    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00003449    00000002     ti_msp_dl_config.o (.rodata.gUART_1ClockConfig)
                  0000344b    00000002     ti_msp_dl_config.o (.rodata.gUART_3ClockConfig)
                  0000344d    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000001ed     UNINITIALIZED
                  20200000    000000a0     (.common:gPWM_6Backup)
                  202000a0    000000a0     (.common:gPWM_7Backup)
                  20200140    00000030     (.common:gUART_3Backup)
                  20200170    00000028     (.common:gSPI_IMU660RBBackup)
                  20200198    00000028     (.common:gTFT_SPIBackup)
                  202001c0    00000018     servo.o (.bss.servo_configs)
                  202001d8    0000000c     (.common:openmvData)
                  202001e4    00000008     openmv.o (.bss.rx_buffer)
                  202001ec    00000001     openmv.o (.bss.data)

.data      0    202001f0    00000018     UNINITIALIZED
                  202001f0    00000004     timer.o (.data.system_time_ms)
                  202001f4    00000002     encoder.o (.data.left_counter)
                  202001f6    00000002     encoder.o (.data.right_counter)
                  202001f8    00000002     empty.o (.data.tft180_bgcolor)
                  202001fa    00000002     openmv.o (.data.tft180_bgcolor)
                  202001fc    00000002     tft180.o (.data.tft180_bgcolor)
                  202001fe    00000002     empty.o (.data.tft180_pencolor)
                  20200200    00000002     openmv.o (.data.tft180_pencolor)
                  20200202    00000001     empty.o (.data.main.test_step)
                  20200203    00000001     openmv.o (.data.n)
                  20200204    00000001     openmv.o (.data.state)
                  20200205    00000001     tft180.o (.data.tft180_x_max)
                  20200206    00000001     tft180.o (.data.tft180_y_max)
                  20200207    00000001     debug.o (.data.uart_data)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2814    128       448    
       empty.o                        668     179       5      
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         3490    499       453    
                                                               
    .\drivers\
       tft180.o                       2240    1520      4      
       openmv.o                       1306    240       27     
       encoder.o                      598     0         4      
       servo.o                        564     0         24     
       timer.o                        230     0         4      
    +--+------------------------------+-------+---------+---------+
       Total:                         4938    1760      63     
                                                               
    .\soft\
       delay.o                        138     0         0      
       debug.o                        78      0         1      
    +--+------------------------------+-------+---------+---------+
       Total:                         216     0         1      
                                                               
    C:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_uart.o                      90      0         0      
       dl_spi.o                       86      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         774     0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     124     0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            40      0         0      
       copy_zero_init.c.obj           22      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       exit.c.obj                     6       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         292     0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         4       0         0      
                                                               
    D:\ti\ccs2020\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatunsisf.S.obj              40      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               2       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         1394    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       52        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   11108   2311      1029   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00003474 records: 2, size/record: 8, table size: 16
	.data: load addr=00003450, load size=00000010 bytes, run addr=202001f0, run size=00000018 bytes, compression=lzss
	.bss: load addr=0000346c, load size=00000008 bytes, run addr=20200000, run size=000001ed bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00003460 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00002c23  ADC0_IRQHandler                 
00002c23  ADC1_IRQHandler                 
00002c23  AES_IRQHandler                  
00002c26  C$$EXIT                         
00002c23  CANFD0_IRQHandler               
00002c23  DAC0_IRQHandler                 
00002c01  DL_Common_delayCycles           
0000203d  DL_SPI_init                     
00002b45  DL_SPI_setClockConfig           
UNDEFED   DL_TimerG_setPeriod             
000010f9  DL_Timer_initFourCCPWMMode      
000011fd  DL_Timer_initTimerMode          
000027b5  DL_Timer_setCaptCompUpdateMethod
0000298d  DL_Timer_setCaptureCompareOutCtl
00002bd5  DL_Timer_setCaptureCompareValue 
000027d1  DL_Timer_setClockConfig         
00001f65  DL_UART_init                    
00002b8d  DL_UART_setClockConfig          
00002c23  DMA_IRQHandler                  
00002c23  Default_Handler                 
00002c23  GROUP0_IRQHandler               
00002c0b  GROUP1_IRQHandler               
00002c27  HOSTexit                        
00002c23  HardFault_Handler               
00002c23  I2C0_IRQHandler                 
00002c23  I2C1_IRQHandler                 
00002c23  NMI_Handler                     
00002c23  PendSV_Handler                  
00002c23  RTC_IRQHandler                  
00002c2b  Reset_Handler                   
00002c23  SPI0_IRQHandler                 
00002c23  SPI1_IRQHandler                 
00002c23  SVC_Handler                     
000009e9  SYSCFG_DL_GPIO_init             
00001569  SYSCFG_DL_PWM_6_init            
000015f5  SYSCFG_DL_PWM_7_init            
000020c5  SYSCFG_DL_SPI_IMU660RB_init     
0000268d  SYSCFG_DL_SYSCTL_init           
00002be5  SYSCFG_DL_SYSTICK_init          
00002105  SYSCFG_DL_TFT_SPI_init          
00002201  SYSCFG_DL_TIMER_12_init         
00002145  SYSCFG_DL_TIMER_8_init          
00001fad  SYSCFG_DL_UART_0_init           
00001e75  SYSCFG_DL_UART_1_init           
00001ff5  SYSCFG_DL_UART_3_init           
00001bcd  SYSCFG_DL_init                  
00001495  SYSCFG_DL_initPower             
00002c23  SysTick_Handler                 
00002c23  TIMA0_IRQHandler                
00002c23  TIMA1_IRQHandler                
00002c23  TIMG0_IRQHandler                
00002809  TIMG12_IRQHandler               
00002c23  TIMG6_IRQHandler                
00002c23  TIMG7_IRQHandler                
000025c9  TIMG8_IRQHandler                
00002b9f  TI_memcpy_small                 
000025f1  UART0_IRQHandler                
00002c23  UART1_IRQHandler                
00002c23  UART2_IRQHandler                
00000ec1  UART3_IRQHandler                
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00003474  __TI_CINIT_Base                 
00003484  __TI_CINIT_Limit                
00003484  __TI_CINIT_Warm                 
00003460  __TI_Handler_Table_Base         
0000346c  __TI_Handler_Table_Limit        
000022b5  __TI_auto_init_nobinit_nopinit  
00001895  __TI_decompress_lzss            
00002bb1  __TI_decompress_none            
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00002aa1  __TI_zero_init_nomemset         
000013c7  __addsf3                        
00001c9d  __aeabi_dcmpeq                  
00001cd9  __aeabi_dcmpge                  
00001ced  __aeabi_dcmpgt                  
00001cc5  __aeabi_dcmple                  
00001cb1  __aeabi_dcmplt                  
00002185  __aeabi_f2d                     
00002369  __aeabi_f2iz                    
000013c7  __aeabi_fadd                    
00001d01  __aeabi_fcmpeq                  
00001d3d  __aeabi_fcmpge                  
00001d51  __aeabi_fcmpgt                  
00001d29  __aeabi_fcmple                  
00001d15  __aeabi_fcmplt                  
00001791  __aeabi_fdiv                    
00001681  __aeabi_fmul                    
000013bd  __aeabi_fsub                    
0000223d  __aeabi_i2f                     
00001e1d  __aeabi_idiv                    
00001813  __aeabi_idiv0                   
00001e1d  __aeabi_idivmod                 
00002c15  __aeabi_memcpy                  
00002c15  __aeabi_memcpy4                 
00002c15  __aeabi_memcpy8                 
00002619  __aeabi_ui2f                    
ffffffff  __binit__                       
00001c35  __cmpdf2                        
000022f1  __cmpsf2                        
00001791  __divsf3                        
00001c35  __eqdf2                         
000022f1  __eqsf2                         
00002185  __extendsfdf2                   
00002369  __fixsfsi                       
0000223d  __floatsisf                     
00002619  __floatunsisf                   
00001a79  __gedf2                         
00002279  __gesf2                         
00001a79  __gtdf2                         
00002279  __gtsf2                         
00001c35  __ledf2                         
000022f1  __lesf2                         
00001c35  __ltdf2                         
000022f1  __ltsf2                         
UNDEFED   __mpu_init                      
0000232d  __muldsi3                       
00001681  __mulsf3                        
00001c35  __nedf2                         
000022f1  __nesf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000013bd  __subsf3                        
00002641  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00002c2f  _system_pre_init                
00002c1d  abort                           
00002c38  ascii_font_8x16                 
ffffffff  binit                           
00002a5f  delay_ms                        
00001aed  delay_us                        
000005d9  encoder_exti_callback           
00000bd5  func_float_to_str               
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
20200140  gUART_3Backup                   
00002bf5  get_system_time_ms              
00000000  interruptVectors                
202001f4  left_counter                    
000000c1  main                            
202001d8  openmvData                      
000023d5  openmv_analysis                 
000007e5  openmv_display_data             
00001911  openmv_init                     
00001ec9  openmv_is_data_valid            
00001dc5  openmv_reset_uart_state         
202001f6  right_counter                   
00001815  servo_config_type               
00002409  servo_get_angle                 
00001989  servo_init                      
000012e5  servo_set_angle                 
00001a01  tft180_clear_color              
00000351  tft180_init                     
00000d91  tft180_show_char_color          
00000fed  tft180_show_num_color           
00001d63  tft180_show_string_color        
00002081  tft180_write_16bit_data         
0000254d  tft180_write_8bit_data          
00001cff  timerA_callback                 
00002a75  timerA_init                     
00001e73  timerB_callback                 
00002a8b  timerB_init                     
20200207  uart_data                       


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  main                            
00000200  __STACK_SIZE                    
00000351  tft180_init                     
000005d9  encoder_exti_callback           
000007e5  openmv_display_data             
000009e9  SYSCFG_DL_GPIO_init             
00000bd5  func_float_to_str               
00000d91  tft180_show_char_color          
00000ec1  UART3_IRQHandler                
00000fed  tft180_show_num_color           
000010f9  DL_Timer_initFourCCPWMMode      
000011fd  DL_Timer_initTimerMode          
000012e5  servo_set_angle                 
000013bd  __aeabi_fsub                    
000013bd  __subsf3                        
000013c7  __addsf3                        
000013c7  __aeabi_fadd                    
00001495  SYSCFG_DL_initPower             
00001569  SYSCFG_DL_PWM_6_init            
000015f5  SYSCFG_DL_PWM_7_init            
00001681  __aeabi_fmul                    
00001681  __mulsf3                        
00001791  __aeabi_fdiv                    
00001791  __divsf3                        
00001813  __aeabi_idiv0                   
00001815  servo_config_type               
00001895  __TI_decompress_lzss            
00001911  openmv_init                     
00001989  servo_init                      
00001a01  tft180_clear_color              
00001a79  __gedf2                         
00001a79  __gtdf2                         
00001aed  delay_us                        
00001bcd  SYSCFG_DL_init                  
00001c35  __cmpdf2                        
00001c35  __eqdf2                         
00001c35  __ledf2                         
00001c35  __ltdf2                         
00001c35  __nedf2                         
00001c9d  __aeabi_dcmpeq                  
00001cb1  __aeabi_dcmplt                  
00001cc5  __aeabi_dcmple                  
00001cd9  __aeabi_dcmpge                  
00001ced  __aeabi_dcmpgt                  
00001cff  timerA_callback                 
00001d01  __aeabi_fcmpeq                  
00001d15  __aeabi_fcmplt                  
00001d29  __aeabi_fcmple                  
00001d3d  __aeabi_fcmpge                  
00001d51  __aeabi_fcmpgt                  
00001d63  tft180_show_string_color        
00001dc5  openmv_reset_uart_state         
00001e1d  __aeabi_idiv                    
00001e1d  __aeabi_idivmod                 
00001e73  timerB_callback                 
00001e75  SYSCFG_DL_UART_1_init           
00001ec9  openmv_is_data_valid            
00001f65  DL_UART_init                    
00001fad  SYSCFG_DL_UART_0_init           
00001ff5  SYSCFG_DL_UART_3_init           
0000203d  DL_SPI_init                     
00002081  tft180_write_16bit_data         
000020c5  SYSCFG_DL_SPI_IMU660RB_init     
00002105  SYSCFG_DL_TFT_SPI_init          
00002145  SYSCFG_DL_TIMER_8_init          
00002185  __aeabi_f2d                     
00002185  __extendsfdf2                   
00002201  SYSCFG_DL_TIMER_12_init         
0000223d  __aeabi_i2f                     
0000223d  __floatsisf                     
00002279  __gesf2                         
00002279  __gtsf2                         
000022b5  __TI_auto_init_nobinit_nopinit  
000022f1  __cmpsf2                        
000022f1  __eqsf2                         
000022f1  __lesf2                         
000022f1  __ltsf2                         
000022f1  __nesf2                         
0000232d  __muldsi3                       
00002369  __aeabi_f2iz                    
00002369  __fixsfsi                       
000023d5  openmv_analysis                 
00002409  servo_get_angle                 
0000254d  tft180_write_8bit_data          
000025c9  TIMG8_IRQHandler                
000025f1  UART0_IRQHandler                
00002619  __aeabi_ui2f                    
00002619  __floatunsisf                   
00002641  _c_int00_noargs                 
0000268d  SYSCFG_DL_SYSCTL_init           
000027b5  DL_Timer_setCaptCompUpdateMethod
000027d1  DL_Timer_setClockConfig         
00002809  TIMG12_IRQHandler               
0000298d  DL_Timer_setCaptureCompareOutCtl
00002a5f  delay_ms                        
00002a75  timerA_init                     
00002a8b  timerB_init                     
00002aa1  __TI_zero_init_nomemset         
00002b45  DL_SPI_setClockConfig           
00002b8d  DL_UART_setClockConfig          
00002b9f  TI_memcpy_small                 
00002bb1  __TI_decompress_none            
00002bd5  DL_Timer_setCaptureCompareValue 
00002be5  SYSCFG_DL_SYSTICK_init          
00002bf5  get_system_time_ms              
00002c01  DL_Common_delayCycles           
00002c0b  GROUP1_IRQHandler               
00002c15  __aeabi_memcpy                  
00002c15  __aeabi_memcpy4                 
00002c15  __aeabi_memcpy8                 
00002c1d  abort                           
00002c23  ADC0_IRQHandler                 
00002c23  ADC1_IRQHandler                 
00002c23  AES_IRQHandler                  
00002c23  CANFD0_IRQHandler               
00002c23  DAC0_IRQHandler                 
00002c23  DMA_IRQHandler                  
00002c23  Default_Handler                 
00002c23  GROUP0_IRQHandler               
00002c23  HardFault_Handler               
00002c23  I2C0_IRQHandler                 
00002c23  I2C1_IRQHandler                 
00002c23  NMI_Handler                     
00002c23  PendSV_Handler                  
00002c23  RTC_IRQHandler                  
00002c23  SPI0_IRQHandler                 
00002c23  SPI1_IRQHandler                 
00002c23  SVC_Handler                     
00002c23  SysTick_Handler                 
00002c23  TIMA0_IRQHandler                
00002c23  TIMA1_IRQHandler                
00002c23  TIMG0_IRQHandler                
00002c23  TIMG6_IRQHandler                
00002c23  TIMG7_IRQHandler                
00002c23  UART1_IRQHandler                
00002c23  UART2_IRQHandler                
00002c26  C$$EXIT                         
00002c27  HOSTexit                        
00002c2b  Reset_Handler                   
00002c2f  _system_pre_init                
00002c38  ascii_font_8x16                 
00003460  __TI_Handler_Table_Base         
0000346c  __TI_Handler_Table_Limit        
00003474  __TI_CINIT_Base                 
00003484  __TI_CINIT_Limit                
00003484  __TI_CINIT_Warm                 
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200000  gPWM_6Backup                    
202000a0  gPWM_7Backup                    
20200140  gUART_3Backup                   
20200170  gSPI_IMU660RBBackup             
20200198  gTFT_SPIBackup                  
202001d8  openmvData                      
202001f4  left_counter                    
202001f6  right_counter                   
20200207  uart_data                       
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   DL_TimerG_setPeriod             
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[180 symbols]
